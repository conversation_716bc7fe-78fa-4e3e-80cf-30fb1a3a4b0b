// src/pages/Dashboard.tsx
import React from 'react';
import { useQuery } from '@apollo/client';
import { gql } from 'graphql-tag';
import type { User } from '../types';

const GET_USER_DATA = gql`
  query GetUserData {
    user {
      id
      name
      email
      role
      createdAt
      updatedAt
    }
  }
`;

const Dashboard: React.FC = () => {
  const { loading, error, data } = useQuery<{ user: User }>(GET_USER_DATA);
  const userString = localStorage.getItem('user');
  const user: User | null = userString ? JSON.parse(userString) : null;

  if (loading) return <p>Loading...</p>;
  if (error) return <p>Error: {error.message}</p>;
  if (!data) return <p>No data found</p>;

  return (
    <div>
      <h2>Dashboard</h2>
      {user && <h3>Welcome, {user.name}!</h3>}
      
      <table>
        <thead>
          <tr>
            <th>Field</th>
            <th>Value</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Name</td>
            <td>{data.user.name}</td>
          </tr>
          <tr>
            <td>Email</td>
            <td>{data.user.email}</td>
          </tr>
          <tr>
            <td>Role</td>
            <td>{data.user.role}</td>
          </tr>
          <tr>
            <td>Created At</td>
            <td>{new Date(data.user.createdAt).toLocaleString()}</td>
          </tr>
          <tr>
            <td>Updated At</td>
            <td>{new Date(data.user.updatedAt).toLocaleString()}</td>
          </tr>
        </tbody>
      </table>
    </div>
  );
};

export default Dashboard;