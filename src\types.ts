// src/types.ts
export interface User {
  id: string;
  name: string;
  email: string;
  username?: string;
  role: string;
  createdAt: string;
  updatedAt: string;
}

export interface AuthResponse {
  token: string;
  user: User;
}

export interface LoginInput {
  username: string;
  password: string;
}

export interface SignupInput {
  name: string;
  email: string;
  username: string;
  password: string;
  role: string;
  confirmed: boolean;
}