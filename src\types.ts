
export interface User {
  id: string;
  username: string;
  email: string;
  role?: {
    name: string;
  };
}

export interface UserDbEntry {
  Name: string;
  email: string;
  createdAt: string;
  updatedAt: string;
}

export interface LoginResponse {
  jwt: string;
  user: User;
}

export interface LoginInput {
  identifier: string;
  password: string;
}

export interface SignupInput {
  username: string;
  email: string;
  password: string;
}

export interface SignupResponse {
  jwt: string;
  user: User;
}